import type { Metadata } from "next";
import localFont from 'next/font/local';

import "./globals.css";
import Sidebar from "@/components/common/Sidebar";
import DashboardNavbar from "@/components/Header/DashboardNavbar";


export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

const myCustomFont = localFont({
  src: [
    {
      path: '../public/assets/fonts/iransans/IRANSansWeb_Medium.woff2', // relative to this file
      weight: '500',
      style: 'normal',
    },
    {
      path: '../public/assets/fonts/iransans/IRANSansWeb_Bold.woff2',
      weight: '700',
      style: 'normal',

    },
  ],
  display: 'swap',
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" className={myCustomFont.className} dir="rtl">
      <body>
        <section className="container mx-auto">
          <div className='flex items-start gap-8 md:p-3 mb-10 '>
            <div className='hidden lg:block h-full'>
              <Sidebar />
            </div>

            <div className='w-full flex flex-col gap-6 md:p-3'>
              <DashboardNavbar />
              {children}
              
            </div>
          </div>

        </section>
      </body>
    </html>
  );
}
