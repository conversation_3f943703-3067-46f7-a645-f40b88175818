import { EditIcon, Menu, SearchIcon, UserIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import profilePicture from '@/public/assets/images/user.webp'

const DashboardNavbar = () => {
  return (
    <>
         <nav
        className="bg-white shadow-md md:rounded-xl lg:py-2 px-5 lg:h-24 py-4 flex items-center justify-between">
        {/* User Profile */}
        <Link href={'/dashboard/profile'} className="user-profile flex items-center gap-4">
          <div className="p-1 border-2 relative border-dashed border-gray-200 rounded-full">
            {profilePicture ? (
              <Image src={profilePicture} alt="profile" width={40} height={40} className="rounded-full" />

            ) : (
              <UserIcon className="lg:w-10 lg:h-10 w-8 h-8" />
            )}
            {/* <UserIcon className="lg:w-10 lg:h-10 w-8 h-8"/> */}
          </div>
          <div className="flex flex-col gap-1">
            <div className="flex gap-4">
              <span
                className="lg:text-base text-sm font-bold"> بدون نام کاربری  </span>
              <span>
                <EditIcon />
              </span>
            </div>
            <span className="text-sm"> 0903528525 </span>
          </div>
        </Link>

        {/* Search & Icons */}
        <div className="flex gap-4 items-center lg:w-[28rem]">
          {/* Search Input (Hidden on Mobile) */}
          <div className="block relative w-full max-md:hidden">
            <input
              type="text"
              placeholder="جستجو..."
              className="w-full py-3 pl-4 pr-10 text-gray-500 bg-gray-100 rounded-full outline-none
              focus:ring-2 focus:ring-gray-300 placeholder-gray-400 text-sm text-right"
              disabled
            />
            <SearchIcon className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 text-lg" />
          </div>

          
          <div className="flex gap-2">
            {/* Menu Button on Mobile, HeartIcon on Desktop */}
            <button
              // onClick={() => setIsSidebarOpen(true)}
              className="bg-gray-100 hover:bg-gray-200 transition-all rounded-full flex items-center justify-center w-10 h-10 lg:hidden"
            >
              <Menu className="size-6" />
            </button>

            
          </div>
        </div>
      </nav>
    </>
  )
}

export default DashboardNavbar