'use client';
import { useState, useRef, useEffect } from "react";
import ArrowDown from "@/public/assets/images/arrow-down.png"
import Image from "next/image";
import { Facebook, Link, Mail, Rss, Triangle, Twitter } from "lucide-react";
import TelegramIcon from "../common/svg/TelegramIcon";
export default function SellerInfo() {
    const [showShare, setShowShare] = useState(false);
    const shareRef = useRef<HTMLDivElement>(null);

    // بستن باکس وقتی بیرون کلیک شد
    useEffect(() => {
        function handleClickOutside(event: MouseEvent) {
            if (shareRef.current && !shareRef.current.contains(event.target as Node)) {
                setShowShare(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
        <div className="p-6 bg-gray-50 rounded-xl border-2 border-dashed border-gray-100 flex items-center gap-16 min-w-96">
            {/* ستون ۱ */}
            <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                    <span className="text-base font-bold">5</span>
                    <span className="text-sm">سال پیش</span>
                </div>
                <span className="text-sm text-gray-400">عضویت</span>
            </div>

            {/* ستون ۲ */}
            <div className="flex flex-col gap-2 border-r-2 border-gray-200 border-dashed pr-6">
                <div className="flex items-center gap-2">
                    <span className="text-base font-bold text-blue-500">99.3 %</span>
                </div>
                <span className="text-sm text-gray-400">رضایت از کالاها</span>
            </div>

            {/* ستون ۳ */}
            <div className="flex flex-col gap-2 border-r-2 border-gray-200 border-dashed pr-6">
                <div className="flex items-center gap-2">
                    <span className="text-base font-bold text-green-400">عالی</span>
                </div>
                <span className="text-sm text-gray-400">عملکرد فروشنده</span>
            </div>

            {/* ستون ۴ - اشتراک گذاری */}
            <div
                className="flex flex-col gap-2 border-r-2 cursor-pointer border-gray-200 border-dashed pr-6 relative"
                ref={shareRef}
                onClick={() => setShowShare(!showShare)}
            >
                <div
                    className="flex items-center justify-center gap-2 "

                >
                    <span>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="size-6 text-gray-400"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 
                1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 
                7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 
                3.935 2.186 2.25 2.25 0 0 
                0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 
                3.933-2.185 2.25 2.25 0 0 0-3.933 
                2.185Z"
                            />
                        </svg>
                    </span>
                </div>
                <span className="text-sm text-gray-400">اشتراک گذاری</span>

                {/* پاپ‌آپ بالای آیکون */}
                {showShare && (
                    <>
                        <div className="absolute -top-[16.5rem] mb-2 left-1/2 -translate-x-1/2 bg-white  rounded-lg p-3 px-0 w-40 z-50">
                            <button className="w-full border-b border-b-gray-300 flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2">
                              <Link size={15} />  کپی لینک
                            </button>
                            <button className="w-full flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2 rounded">
                              <TelegramIcon size={15} />  اشتراک در تلگرام
                            </button>
                            <button className="w-full flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2 rounded">
                              <Facebook fill="" size={15} />  اشتراک در فیسبوک
                            </button>
                            <button className="w-full flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2 rounded">
                              <Twitter fill="" size={15} />  اشتراک در توییتر
                            </button>
                            <button className="w-full flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2 rounded">
                              <Mail   className="text-gray-400" size={15} />  اشتراک به ایمیل 
                            </button>
                            <button className="w-full flex items-center gap-2 text-right text-sm hover:bg-gray-100 px-2 py-2 rounded">
                              <Rss size={15} />  لینک آر اس اس
                            </button>
                           
                            
                            <div className="relative">
                                <Triangle stroke="white" fill="white" className="absolute -bottom-[31px] right-1/2 translate-x-1/2 rotate-180" />
                            </div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}
