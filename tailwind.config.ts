module.exports = {
  darkMode: "class",
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // main colors
        background: "#f5f6f8",
        foreground: "#62676E",
        primary: {
          DEFAULT: "#1F84FB",
          foreground: "#0A0A0C"
        },
        secondary: {
          DEFAULT: "#F5F6F8",
          foreground: "#15192A",
          grey: "#F5F6F8"
        },
        muted: {
          DEFAULT: "#F9FAFB",
          foreground: "#9DA5B0"
        },
        accent: {
          DEFAULT: "#F5F6F8",
          foreground: "#15192A"
        },
        destructive: {
          DEFAULT: "#E53E3E",
          foreground: "#F7FAFC"
        },
        warning: {
          DEFAULT: "#F7BC06"
        },
        border: "#E4E6E9",
        input: "#EDF2F7",
        ring: "#1A202C",
        
        // chart colors
        chart: {
          1: "#F56565",
          2: "#38B2AC",
          3: "#2C5282",
          4: "#ECC94B",
          5: "#ED8936"
        }
      },
      borderRadius: {
        lg: "0.5rem",
        md: "calc(0.5rem - 2px)",
        sm: "calc(0.5rem - 4px)"
      },
      // rest of the settings
    },
    dark: {
      colors: {
        // dark mode colors
        background: "#1A202C",
        foreground: "#F7FAFC",
        primary: {
          DEFAULT: "#F7FAFC",
          foreground: "#1A202C"
        },
        secondary: {
          DEFAULT: "#2D3748",
          foreground: "#F7FAFC"
        },
        muted: {
          DEFAULT: "#2D3748",
          foreground: "#A0AEC0"
        },
        accent: {
          DEFAULT: "#2D3748",
          foreground: "#F7FAFC"
        },
        destructive: {
          DEFAULT: "#C53030",
          foreground: "#F7FAFC"
        },
        border: "#2D3748",
        input: "#2D3748",
        ring: "#CBD5E0"
      }
    }
  },
  plugins: [
    require('tailwindcss-animate'),
    require('@tailwindcss/typography')
  ]
}