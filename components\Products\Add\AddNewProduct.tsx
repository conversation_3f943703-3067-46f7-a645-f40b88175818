"use client";
import DropdownSearch from '@/components/common/DropdownSearch'
import { FilePlus } from 'lucide-react'
import React, { useState } from 'react'
interface DropdownItem {
  id: string | number;
  label: string;
//   [key: string]: any;
}
const AddNewProduct = () => {
     const [selectedItem, setSelectedItem] = useState<DropdownItem | null>(null);

  const items = [
    { id: 1, label: " خودرو" },
    { id: 2, label: "لپتاپ" },
    { id: 3, label: "آیتم ۳" },
    // ...
  ];
    return (
        <div className='bg-white p-3 px-5 rounded-xl shadow-md overflow-hidden flex flex-col gap-5'>
            <div className='add-product-header flex items-center gap-3'>
                <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                    <FilePlus />
                </div>
                <div className='w-full flex items-center gap-1'>
                    <h2 className=''>افزودن محصول جدید</h2>
                    <div className=' title-line h-px bg-gray-300 w-[70%]'></div>
                </div>
            </div>
            <div className='p-4  my-6 rounded-xl bg-orange-50 border border-yellow-400 border-dashed space-y-4 text-sm leading-6 text-gray-700'>
                <h4>افزودن محصول اختصاصی</h4>
                <p>
                    از اینجا میتوانید محصول خود را که در سایت خودروکس نیست را اضافه کنید و بعد از برسی و تائید در سایت منتشر کنید
                </p>
            </div>
            <div className="mt-5">
                <DropdownSearch
                    items={items}
                    placeholder="یک آیتم انتخاب کنید"
                    // searchPlaceholder="جستجو..."
                    onSelect={setSelectedItem}
                    selectedItem={selectedItem}
                    className="w-64"
                    useModal={true}
                    categories={items}
                />
            </div>
            <div className='mt-5'>
                <button className='bg-primary text-white px-5 py-3.5 rounded-2xl'>
                    شروع و ایجاد محصول جدید
                </button>
            </div>
        </div>
    )
}

export default AddNewProduct