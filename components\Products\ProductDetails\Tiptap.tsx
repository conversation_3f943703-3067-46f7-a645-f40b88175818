'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Link from '@tiptap/extension-link'
import { useEffect, useState } from 'react'
import clsx from 'clsx'
import {
  Bold, Italic, Heading1, Heading2, Quote, List, ListOrdered,
  Code, Undo2, Redo2, Link as LinkIcon, Minus
} from 'lucide-react'

const RichTextEditor = () => {
  const [mounted, setMounted] = useState(false)
  
  useEffect(() => {
    setMounted(true)
  }, [])

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2],
        },
      }),
      Link.configure({
        openOnClick: false,
        autolink: true,
      }),
    ],
    content: '<p>محصولت رو اینجا وارد کن...</p>',
    editorProps: {
      attributes: {
        class: 'prose prose-sm rtl text-right max-w-none p-4 outline-none min-h-[150px]',
      },
    },
    autofocus: true,
    immediatelyRender: true, // برای نسخه‌هایی که این گزینه را می‌پذیرند
  })

  useEffect(() => {
    return () => {
      editor?.destroy()
    }
  }, [editor])

  if (!mounted || !editor) return <div className="border rounded-xl bg-white p-4 min-h-[150px]">در حال بارگذاری ویرایشگر...</div>

  const isActive = (type: string, opts = {}) =>
    editor.isActive(type, opts)

  const btnClass = (active: boolean) =>
    clsx(
      'p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed',
      active && 'bg-gray-200'
    )

  return (
    <div className="border rounded-xl bg-white shadow-sm">
      {/* Toolbar */}
      <div className="flex flex-wrap items-center gap-2 border-b p-2 bg-gray-50">
        {/* Bold */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleBold().run()
          }}
          className={btnClass(isActive('bold'))}
          disabled={!editor.isEditable}
          title="پررنگ"
        >
          <Bold size={16} />
        </button>

        {/* Italic */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleItalic().run()
          }}
          className={btnClass(isActive('italic'))}
          disabled={!editor.isEditable}
          title="ایتالیک"
        >
          <Italic size={16} />
        </button>

        {/* Heading 1 */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleHeading({ level: 1 }).run()
          }}
          className={btnClass(isActive('heading', { level: 1 }))}
          disabled={!editor.isEditable}
          title="عنوان ۱"
        >
          <Heading1 size={16} />
        </button>

        {/* Heading 2 */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleHeading({ level: 2 }).run()
          }}
          className={btnClass(isActive('heading', { level: 2 }))}
          disabled={!editor.isEditable}
          title="عنوان ۲"
        >
          <Heading2 size={16} />
        </button>

        {/* Bullet List */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleBulletList().run()
          }}
          className={btnClass(isActive('bulletList'))}
          disabled={!editor.isEditable}
          title="لیست نقطه‌ای"
        >
          <List size={16} />
        </button>

        {/* Ordered List */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleOrderedList().run()
          }}
          className={btnClass(isActive('orderedList'))}
          disabled={!editor.isEditable}
          title="لیست شماره‌ای"
        >
          <ListOrdered size={16} />
        </button>

        {/* Code Block */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleCodeBlock().run()
          }}
          className={btnClass(isActive('codeBlock'))}
          disabled={!editor.isEditable}
          title="بلوک کد"
        >
          <Code size={16} />
        </button>

        {/* Blockquote */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().toggleBlockquote().run()
          }}
          className={btnClass(isActive('blockquote'))}
          disabled={!editor.isEditable}
          title="نقل‌قول"
        >
          <Quote size={16} />
        </button>

        {/* Horizontal Rule */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().setHorizontalRule().run()
          }}
          className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!editor.isEditable}
          title="خط افقی"
        >
          <Minus size={16} />
        </button>

        {/* Link */}
        <button
          onClick={(e) => {
            e.preventDefault()
            const previousUrl = editor.getAttributes('link').href
            const url = prompt('آدرس لینک:', previousUrl || 'https://')
            
            if (url === null) return
            
            if (url === '') {
              editor.chain().focus().extendMarkRange('link').unsetLink().run()
              return
            }
            
            editor
              .chain()
              .focus()
              .extendMarkRange('link')
              .setLink({ href: url })
              .run()
          }}
          className={btnClass(isActive('link'))}
          disabled={!editor.isEditable}
          title="لینک"
        >
          <LinkIcon size={16} />
        </button>

        {/* Undo */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().undo().run()
          }}
          className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!editor.can().undo() || !editor.isEditable}
          title="بازگردانی"
        >
          <Undo2 size={16} />
        </button>

        {/* Redo */}
        <button
          onClick={(e) => {
            e.preventDefault()
            editor.chain().focus().redo().run()
          }}
          className="p-1 hover:bg-gray-100 rounded disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!editor.can().redo() || !editor.isEditable}
          title="انجام مجدد"
        >
          <Redo2 size={16} />
        </button>
      </div>

      {/* Editor content */}
      <EditorContent editor={editor} />
    </div>
  )
}

export default RichTextEditor