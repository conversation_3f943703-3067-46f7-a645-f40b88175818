"use client";

import { useState } from "react";
import DropdownSearch, { Category } from "./DropdownSearch";

/**
 * Example usage of the DropdownSearch component with modal functionality
 * This shows how to implement hierarchical category selection
 */

// Sample category data with nested structure
const sampleCategories: Category[] = [
  {
    id: 1,
    label: "خودرو",
    children: [
      {
        id: 11,
        label: "سواری",
        children: [
          { id: 111, label: "پژو" },
          { id: 112, label: "سمند" },
          { id: 113, label: "پراید" },
          { id: 114, label: "دنا" },
        ]
      },
      {
        id: 12,
        label: "وانت",
        children: [
          { id: 121, label: "پیکان وانت" },
          { id: 122, label: "زامیاد" },
        ]
      },
      {
        id: 13,
        label: "موتورسیکلت",
        children: [
          { id: 131, label: "هوندا" },
          { id: 132, label: "یاماها" },
          { id: 133, label: "باجاج" },
        ]
      }
    ]
  },
  {
    id: 2,
    label: "املاک",
    children: [
      {
        id: 21,
        label: "فروش مسکونی",
        children: [
          { id: 211, label: "آپارتمان" },
          { id: 212, label: "خانه ویلایی" },
          { id: 213, label: "زمین مسکونی" },
        ]
      },
      {
        id: 22,
        label: "اجاره مسکونی",
        children: [
          { id: 221, label: "آپارتمان" },
          { id: 222, label: "خانه ویلایی" },
          { id: 223, label: "سوئیت و استودیو" },
        ]
      },
      {
        id: 23,
        label: "فروش اداری و تجاری",
        children: [
          { id: 231, label: "دفتر کار" },
          { id: 232, label: "مغازه" },
          { id: 233, label: "صنعتی و کشاورزی" },
        ]
      }
    ]
  },
  {
    id: 3,
    label: "کالای دیجیتال",
    children: [
      {
        id: 31,
        label: "موبایل",
        children: [
          { id: 311, label: "سامسونگ" },
          { id: 312, label: "اپل" },
          { id: 313, label: "شیائومی" },
          { id: 314, label: "هوآوی" },
        ]
      },
      {
        id: 32,
        label: "تبلت",
        children: [
          { id: 321, label: "آیپد" },
          { id: 322, label: "تبلت سامسونگ" },
          { id: 323, label: "تبلت لنوو" },
        ]
      },
      {
        id: 33,
        label: "لپ‌تاپ",
        children: [
          { id: 331, label: "ایسوس" },
          { id: 332, label: "اچ پی" },
          { id: 333, label: "لنوو" },
          { id: 334, label: "مک‌بوک" },
        ]
      }
    ]
  },
  {
    id: 4,
    label: "خدمات",
    children: [
      { id: 41, label: "آموزش" },
      { id: 42, label: "طراحی سایت" },
      { id: 43, label: "تعمیرات" },
      { id: 44, label: "حمل و نقل" },
    ]
  }
];

const CategoryDropdownExample = () => {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category);
    console.log("Selected category:", category);
  };

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-4">انتخاب دسته‌بندی با مودال</h2>
        
        <DropdownSearch
          useModal={true}
          categories={sampleCategories}
          placeholder="دسته‌بندی محصول را انتخاب کنید"
          onSelect={handleCategorySelect}
          selectedItem={selectedCategory}
          className="mb-4"
        />

        {selectedCategory && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">
              <strong>دسته‌بندی انتخاب شده:</strong> {selectedCategory.label}
            </p>
            <p className="text-xs text-green-600 mt-1">
              ID: {selectedCategory.id}
            </p>
          </div>
        )}
      </div>

      <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">راهنمای استفاده:</h3>
        <ul className="space-y-1 text-xs">
          <li>• روی فیلد ورودی کلیک کنید تا مودال باز شود</li>
          <li>• از قسمت جستجو برای یافتن دسته‌بندی استفاده کنید</li>
          <li>• روی دسته‌بندی‌هایی که زیرمجموعه دارند کلیک کنید</li>
          <li>• برای بازگشت از breadcrumb استفاده کنید</li>
          <li>• روی دسته‌بندی نهایی کلیک کنید تا انتخاب شود</li>
        </ul>
      </div>
    </div>
  );
};

export default CategoryDropdownExample;
