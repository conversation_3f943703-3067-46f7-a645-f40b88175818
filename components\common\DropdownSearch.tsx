"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, X, Check, Search, ChevronRight, ArrowRight } from "lucide-react";

export interface Category {
  id: string | number;
  label: string;
  children?: Category[];
}

export interface DropdownItem {
  id: string | number;
  label: string;
  // [key: string]: any;
}

interface DropdownSearchProps {
  items?: DropdownItem[];
  categories?: Category[];
  placeholder?: string;
  onSelect: (item: DropdownItem | Category) => void;
  selectedItem?: DropdownItem | Category | null;
  className?: string;
  useModal?: boolean; // New prop to enable modal mode
}

/**
 * CategoryModal component for hierarchical category selection
 */
interface CategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  categories: Category[];
  onSelect: (category: Category) => void;
}

const CategoryModal = ({ isOpen, onClose, categories, onSelect }: CategoryModalProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentLevel, setCurrentLevel] = useState<Category[]>(categories);
  const [breadcrumb, setBreadcrumb] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>(categories);

  // Filter categories based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredCategories(currentLevel);
    } else {
      const filterRecursive = (cats: Category[]): Category[] => {
        return cats.filter(cat =>
          cat.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (cat.children && filterRecursive(cat.children).length > 0)
        );
      };
      setFilteredCategories(filterRecursive(currentLevel));
    }
  }, [searchTerm, currentLevel]);

  // Reset modal state when opened
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
      setCurrentLevel(categories);
      setBreadcrumb([]);
      setFilteredCategories(categories);
    }
  }, [isOpen, categories]);

  const handleCategoryClick = (category: Category) => {
    if (category.children && category.children.length > 0) {
      // Navigate to subcategories
      setCurrentLevel(category.children);
      setBreadcrumb([...breadcrumb, category]);
      setSearchTerm("");
    } else {
      // Final selection - close modal
      onSelect(category);
      onClose();
    }
  };

  const handleBreadcrumbClick = (index: number) => {
    if (index === -1) {
      // Go back to root
      setCurrentLevel(categories);
      setBreadcrumb([]);
    } else {
      // Go back to specific level
      const newBreadcrumb = breadcrumb.slice(0, index + 1);
      setBreadcrumb(newBreadcrumb);
      setCurrentLevel(newBreadcrumb[newBreadcrumb.length - 1].children || categories);
    }
    setSearchTerm("");
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={onClose}>
      <div className="bg-white rounded-2xl w-full max-w-md mx-4 max-h-[80vh] flex flex-col" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">انتخاب دسته‌بندی</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={24} />
            </button>
          </div>

          {/* Search Input */}
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="جستجو در دسته‌بندی‌ها..."  
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pr-10 pl-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
              dir="rtl"
            />
          </div>
        </div>

        {/* Breadcrumb */}
        {breadcrumb.length > 0 && (
          <div className="px-6 py-3 border-b border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600" dir="rtl">
              <button
                onClick={() => handleBreadcrumbClick(-1)}
                className="hover:text-blue-600 transition-colors"
              >
                همه دسته‌ها
              </button>
              {breadcrumb.map((item, index) => (
                <div key={item.id} className="flex items-center gap-2">
                  <ChevronRight size={16} className="text-gray-400" />
                  <button
                    onClick={() => handleBreadcrumbClick(index)}
                    className="hover:text-blue-600 transition-colors"
                  >
                    {item.label}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Categories List */}
        <div className="flex-1 overflow-y-auto p-6">
          {filteredCategories.length > 0 ? (
            <div className="space-y-2">
              {filteredCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => handleCategoryClick(category)}
                  className="w-full flex items-center justify-between p-3 text-right hover:bg-gray-50 rounded-xl transition-colors group"
                >
                  <span className="text-gray-900">{category.label}</span>
                  {category.children && category.children.length > 0 && (
                    <ArrowRight size={16} className="text-gray-400 group-hover:text-gray-600" />
                  )}
                </button>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              دسته‌بندی‌ای یافت نشد
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const DropdownSearch = ({
  items = [],
  categories = [],
  placeholder = "جستجو یا انتخاب کنید",
  onSelect,
  selectedItem,
  className = "",
  useModal = false,
}: DropdownSearchProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState<DropdownItem[]>(items);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (!useModal) {
      setFilteredItems(
        items.filter((item) =>
          item.label.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm, items, useModal]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (item: DropdownItem | Category) => {
    debugger
    onSelect(item);
    setIsOpen(false);
    setIsModalOpen(false);
    setSearchTerm("");
    if (!useModal) {
      setFilteredItems(items);
    }
  };

  const handleInputClick = () => {
    debugger
    if (useModal) {
      setIsModalOpen(true);
    } else {
      setIsOpen(true);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!useModal) {
      setSearchTerm(e.target.value);
      if (!isOpen) setIsOpen(true);
    }
  };

  const clearSelection = () => {
    debugger
    onSelect({} as DropdownItem | Category);
    setSearchTerm("");
    inputRef.current?.focus();
  };

  const displayValue = selectedItem ? selectedItem.label : (useModal ? "" : searchTerm);

  return (
    <>
      <div
        ref={dropdownRef}
        className={`relative w-full ${className}`}
        dir="rtl"
      >
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            className="w-full px-4 py-4 pr-10 text-sm border border-gray-300 rounded-xl bg-gray-50/50 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
            placeholder={placeholder}
            value={displayValue}
            onChange={handleInputChange}
            onClick={handleInputClick}
            readOnly={useModal}
            autoComplete="off"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
            {selectedItem && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  clearSelection();
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
            <ChevronDown
              size={16}
              className={`text-gray-400 transition-transform duration-200 ${
                (isOpen || isModalOpen) ? "rotate-180" : ""
              }`}
            />
          </div>
        </div>

        {/* Regular dropdown for non-modal mode */}
        {!useModal && isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
            <div className="max-h-60 overflow-y-auto">
              {filteredItems.length > 0 ? (
                filteredItems.map((item) => (
                  <button
                    key={item.id}
                    type="button"
                    className={`flex items-center justify-between w-full px-4 py-2 text-sm text-right hover:bg-gray-100 ${
                      selectedItem?.id === item.id ? "bg-blue-50" : ""
                    }`}
                    onClick={() => handleSelect(item)}
                  >
                    <span>{item.label}</span>
                    {selectedItem?.id === item.id && (
                      <Check size={16} className="text-blue-500" />
                    )}
                  </button>
                ))
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 text-center">
                  موردی یافت نشد
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Category Modal for modal mode */}
      {useModal && (
        <CategoryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          categories={categories}
          onSelect={handleSelect}
        />
      )}
    </>
  );
};

export default DropdownSearch;