"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, X, Check } from "lucide-react";

export interface DropdownItem {
  id: string | number;
  label: string;
  // [key: string]: any;
}

interface DropdownSearchProps {
  items: DropdownItem[];
  placeholder?: string;
  onSelect: (item: DropdownItem) => void;
  selectedItem?: DropdownItem | null;
  className?: string;
}

const DropdownSearch = ({
  items,
  placeholder = "جستجو یا انتخاب کنید",
  onSelect,
  selectedItem,
  className = "",
}: DropdownSearchProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredItems, setFilteredItems] = useState(items);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setFilteredItems(
      items.filter((item) =>
        item.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm, items]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (item: DropdownItem) => {
    onSelect(item);
    setIsOpen(false);
    setSearchTerm("");
    setFilteredItems(items);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (!isOpen) setIsOpen(true);
  };

  const clearSelection = () => {
    onSelect(null as any);
    setSearchTerm("");
    inputRef.current?.focus();
  };

  const displayValue = selectedItem ? selectedItem.label : searchTerm;

  return (
    <div
      ref={dropdownRef}
      className={`relative w-full ${className}`}
      dir="rtl"
    >
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          className="w-full px-4 py-4 pr-10 text-sm border border-gray-300 rounded-xl bg-gray-50/50 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder={placeholder}
          value={displayValue}
          onChange={handleInputChange}
          onClick={() => setIsOpen(true)}
          autoComplete="off"
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {selectedItem && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={16} />
            </button>
          )}
          <ChevronDown
            size={16}
            className={`text-gray-400 transition-transform duration-200 ${
              isOpen ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden">
          <div className="max-h-60 overflow-y-auto">
            {filteredItems.length > 0 ? (
              filteredItems.map((item) => (
                <button
                  key={item.id}
                  type="button"
                  className={`flex items-center justify-between w-full px-4 py-2 text-sm text-right hover:bg-gray-100 ${
                    selectedItem?.id === item.id ? "bg-blue-50" : ""
                  }`}
                  onClick={() => handleSelect(item)}
                >
                  <span>{item.label}</span>
                  {selectedItem?.id === item.id && (
                    <Check size={16} className="text-blue-500" />
                  )}
                </button>
              ))
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500 text-center">
                موردی یافت نشد
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DropdownSearch;