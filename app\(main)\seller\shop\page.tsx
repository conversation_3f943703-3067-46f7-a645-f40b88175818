import Image from 'next/image'
import SellerImage from "@/public/assets/images/seller-image.png"
import Rating from "@/public/assets/images/rating.png"
import { ArrowDownNarrowWide, ChevronLeft, Star, Store, Trash2 } from 'lucide-react'
import SellerInfo from '@/components/Seller/SellerInfo'
import SortBar from '@/components/Seller/SortBar'
import Product from '@/components/Seller/Product'
import AccordionHeader from '@/components/common/AccirdionHeader'
import Link from 'next/link'

const page = () => {
    return (
        <>
            <div className='h-80 bg-gray-200 relative'>
                <Image src={SellerImage} fill alt='seller' className='object-cover ' />
            </div>
            <div className='container mx-auto'>
                <div className='relative -top-16 p-5'>
                    <div className='bg-white rounded-2xl shadow-2xs p-5'>
                        <div className='w-full flex items-center justify-between'>
                            {/* سمت راست باکس سفید */}
                            <div className='flex items-center gap-2'>
                                <div className='bg-gray-200 rounded-full p-1 w-16 h-16'></div>
                                <div className='flex flex-col gap-2'>
                                    <p className='font-bold text-xl'>سپهر پلاس</p>
                                    <div className=' flex items-center gap-3'>
                                        <span className='bg-blue-50 flex items-center gap-2 rounded-3xl p-1 px-2'>
                                            <span className='w-4 h-4 bg-blue-500 rounded-full'></span>
                                            <span className='text-xs text-blue-400'>فروشنده تایید شده</span>
                                        </span>
                                        <div className='flex items-center gap-2'>
                                            <Star color='#F7BC06' fill='#F7BC06' size={20} />
                                            <span >
                                                3 از 5
                                            </span>
                                            <span>
                                                (408)
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* سمت چپ باکس سفید */}
                            {/* <div className='p-6 bg-gray-50 rounded-xl border-2 border-dashed border-gray-100 flex items-center gap-16 min-w-96'>
                                <div className='flex flex-col gap-2'>
                                    <div className='flex items-center gap-2'>
                                        <span className='text-base font-bold'>5</span>
                                        <span className='text-sm'>سال پیش</span>
                                    </div>
                                    <span className='text-sm text-gray-400'>عضویت</span>
                                </div>
                                <div className='flex flex-col gap-2 border-r-2 border-gray-200 border-dashed pr-6'>
                                    <div className='flex items-center gap-2'>
                                        <span className='text-base font-bold text-blue-500'>99.3 %</span>
                                    </div>
                                    <span className='text-sm text-gray-400'>رضایت از کالاها</span>
                                </div>
                                <div className='flex flex-col gap-2 border-r-2 border-gray-200 border-dashed pr-6'>
                                    <div className='flex items-center gap-2'>
                                        <span className='text-base font-bold text-green-400 '>عالی</span>
                                    </div>
                                    <span className='text-sm text-gray-400'>عملکرد فروشنده</span>
                                </div>
                                <div className='flex flex-col gap-2 border-r-2 border-gray-200 border-dashed pr-6'>
                                    <div className='flex items-center justify-center gap-2'>
                                        <span>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="size-6 text-gray-400">
                                                <path strokeLinecap="round" strokeLinejoin="round" d="M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z" />
                                            </svg>
                                        </span>
                                    </div>
                                    <span className='text-sm text-gray-400'>اشتراک گذاری</span>
                                </div>

                            </div> */}
                            <SellerInfo />

                        </div>
                    </div>
                </div>
                <div className='grid grid-cols-12 gap-4 min-h-[22rem]'>

                    <div className='col-span-8'>
                        <div className='grid grid-rows-7 gap-3 h-full'>
                            <div className='row-span-4 bg-white rounded-2xl shadow-2xs p-4'>
                                <div className='flex items-center gap-3'>
                                    <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                                        <Store />
                                    </div>
                                    <h1 className='md:text-lg text-base'>اطلاعات فروشگاه</h1>
                                </div>
                                <p className='text-sm text-gray-500 text-justify mt-3 leading-8'>لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است. چاپگرها و متون بلکه روزنامه و مجله در ستون و سطرآنچنان که لازم است و برای شرایط فعلی تکنولوژی مورد نیاز و کاربردهای متنوع با هدف بهبود ابزارهای کاربردی می باشد. کتابهای زیادی در شصت و سه درصد گذشته، حال و آینده شناخت فراوان جامعه و متخصصان را می طلبد تا با نرم افزارها شناخت بیشتری را برای طراحان رایانه ای علی الخصوص طراحان خلاقی و فرهنگ پیشرو در زبان فارسی ایجاد کرد. </p>
                            </div>
                            <div className='row-span-3 relative bg-white flex justify-end rounded-2xl border border-gray-200'>
                                <div className='bg-gray-50 p-2 h-[80%] mx-5 my-auto z-10 rounded-lg w-[70%]'>
                                    <div className='flex gap-8 items-center h-full bg-white p-2 rounded-lg px-5 seller-stats'>
                                        <div className='border-l border-gray-200'>
                                            <h4>
                                                70%
                                            </h4>
                                            <span className='text-sm'>
                                                تامین به موقع
                                            </span>
                                        </div>
                                        <div className='border-l border-gray-200'>
                                            <h4>
                                                100%
                                            </h4>
                                            <span className='text-sm'>
                                                تعهد ارسال
                                            </span>
                                        </div>
                                        <div className='border-l border-gray-200'>
                                            <h4>
                                                88%
                                            </h4>
                                            <span className='text-sm'>
                                                بدون مرجوعی
                                            </span>
                                        </div>
                                        <div>
                                            <h4>
                                                8 ساعت
                                            </h4>
                                            <span className='text-sm'>
                                                زمان پاسخگویی
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className='absolute p-5 px-8 text-white top-1.5 right-4 w-96 h-[90%] bg-green-700 z-0 rounded-3xl flex flex-col'>
                                    <h4 className='text-lg'>عالی</h4>
                                    <p className='text-sm'>
                                        عملکرد کلی فروشنده
                                    </p>

                                </div>
                            </div>
                        </div>


                    </div>
                    <div className='col-span-4 border border-gray-200 bg-white cart-circles-yellow rounded-3xl p-2 flex justify-center items-center flex-col gap-1'>
                        <div className='relative w-44 h-44'>
                            <Image src={Rating} alt='rating' fill />
                            <h2 className='text-warning absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-2xl'> 68% </h2>
                        </div>
                        <div className='p-4 py-3  mb-3 rounded-xl bg-orange-50 border border-yellow-400 border-dashed text-sm leading-6 text-gray-700'>
                            <p>
                                رضایت خریداران از کالا
                            </p>
                        </div>
                        <div className='flex items-center gap-3 mb-1'>
                            <div className='flex gap-1'>
                                <Star color='#F7BC06' fill='#F7BC06' size={18} />
                                <Star color='#F7BC06' fill='#F7BC06' size={18} />
                                <Star color='#F7BC06' fill='#F7BC06' size={18} />
                                <Star fill='#9DA5B0' color='#9DA5B0' size={18} />
                                <Star fill='#9DA5B0' color='#9DA5B0' size={18} />
                            </div>
                            <span className='text-sm'>
                                3 از 5
                            </span>
                        </div>
                        <span className='text-sm'>
                            (408) نفر امتیاز داده
                        </span>
                    </div>
                </div>
            </div>
            <div className='container mt-10 mx-auto mb-10 grid grid-cols-12 items-start'>
                <div className='md:col-span-3'>
                    <div className=" py-8 bg-white flex justify-center md:justify-between items-center gap-6 md:px-5 max-md:text-sm rounded-3xl px-1 max-md:hidden">
                        <span className="flex flex-row-reverse gap-2 items-center ">
                            فیلترها
                            <ArrowDownNarrowWide className="md:hidden" />
                        </span>
                        <button
                            className="bg-[#FF4A4A] max-md:hidden max-md:mx-1 text-white px-4 py-2 rounded-3xl flex flex-row-reverse gap-2 whitespace-nowrap text-sm items-center">
                            پاک کردن <Trash2 className="w-5" />
                        </button>
                    </div>
                    <div
                        className='mt-5 bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter flex justify-between'>
                        <label className="flex w-full items-center cursor-pointer justify-between">
                            <span className="ms-3 text-sm font-medium  text-gray-900 dark:text-gray-300"> فقط محصولات موجود </span>
                            <input type="checkbox"
                                // value={value}
                                // checked={value === 'true'}
                                // onChange={handleChange}
                                className="sr-only peer" />
                            <div
                                className="relative w-11 h-6 bg-gray-200  rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 "></div>

                        </label>
                    </div>
                    <div
                        className='mt-5 bg-white max-md:border max-md:mb-3 border-gray-200 min-h-20 rounded-3xl p-3 search-products-filter flex justify-between'>
                        <label className="flex w-full items-center cursor-pointer justify-between">
                            <span className="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300"> محصولات گارانتی دار </span>
                            <input
                                type="checkbox"
                                // value={value}
                                // checked={value === 'true'}
                                // onChange={handleChange}
                                className="sr-only peer" />
                            <div
                                className="relative w-11 h-6 bg-gray-200  rounded-full peer peer-checked:after:translate-x-full
                                rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-['']
                                after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300
                                after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 "></div>
                        </label>
                    </div>
                    <div className="bg-white min-h-20 rounded-3xl p-3 search-products-filter mt-5">
                        <AccordionHeader
                            showCloseBtn={false}
                            showToggleBtn={true}
                            // onCloseBtnClick={onClose}
                            open={true}
                            title="دسته بندی"
                        >
                            <ul className="flex flex-col gap-5 mb-3 text-sm h-fit">
                                <li className="flex flex-col gap-2 pl-5">
                                    <div className="flex items-center justify-between relative">
                                        <Link  href={`/category/`}>
                                            عنوان فیلتر
                                        </Link>

                                        {/* {hasChildren && ( */}
                                            <button  className="flex justify-end relative">
                                                <ChevronLeft
                                                    className={`text-gray-400 transition-transform duration-200 `}
                                                />
                                                <ChevronLeft
                                                    className={`text-gray-300 absolute -left-2 top-0 transition-transform duration-200 `}
                                                />
                                            </button>
                                        {/* )} */}
                                    </div>

                                    {/* {hasChildren && isOpen && (
                                        <ul className="flex flex-col gap-y-5 border-r border-dashed pr-5 mt-1">
                                            {(category?.children || []).map((child) => (
                                                <CategoryItem key={child.slug} category={child} onClose={onClose} />
                                            ))}
                                        </ul>
                                    )} */}
                                </li>
                            </ul>
                        </AccordionHeader>
                    </div>

                </div>

                {/* فاصله بین ستون‌ها */}
                <div className='col-span-1'></div>

                <div className='col-span-8'>
                    <SortBar />
                    <div className='flex justify-between items-center flex-wrap mt-5'>
                        <Product />
                        <Product />
                        <Product />
                    </div>
                </div>
            </div>

        </>
    )
}

export default page