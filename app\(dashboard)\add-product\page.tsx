import AddNewProductContainer from "@/components/Products/Add/AddNewProductContainer";
import ProductFormContainer from "@/components/Products/ProductDetails/ProductFormContainer";

const page = async ({ searchParams }: { searchParams: Promise<{ step?: string }>} ) => {
  const { step } = await searchParams; 
  const currentStep = step ?? "1"; 
  console.log(currentStep);
     

  if (currentStep == "1") {
    return <AddNewProductContainer />
    
  }
  if (currentStep == "2") {
    return <ProductFormContainer />
  }
  return (
    <div>
      <AddNewProductContainer /> :
    </div>
  );
};

export default page;
