import { Camera, Store } from 'lucide-react'
import ProfilePicture from "@/public/assets/images/user.webp"
import NeshanAddressForm from '@/components/shopInfo/NeshanAddressForm'


const ShopInfoPage = () => {
    return (
        <div className='bg-white rounded-xl p-3'>
            <div className='flex items-center gap-3'>
                <div className='bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3  rounded-b-4xl'>
                    <Store />
                </div>
                <h1 className='md:text-lg text-base'>اطلاعات فروشگاه</h1>
            </div>
            <div>
                <div className='p-4 py-3  my-6 rounded-xl bg-orange-50 border border-yellow-400 border-dashed text-sm leading-6 text-gray-700'>
                    <p>
                        برای اینکه فروشگاه شما وجهه بهتری داشته باشد، اطلاعات زیر را تکمیل کنید
                    </p>
                </div>
                <div className='forms grid grid-cols-2 gap-8'>
                    <div className='flex flex-col gap-3'>
                        <label htmlFor="name"> نام فروشگاه یا فروشنده </label>
                        <input type="text" name='name' className='border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                    </div>
                    <div className='flex flex-col gap-3'>
                        <label htmlFor="name"> نوع فروشنده  </label>
                        <input type="text" name='name' className='border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                    </div>
                    <div className="group relative col-span-2">
                        <input
                            type="file"
                            id="profilePhoto"
                            accept=".jpg,.jpeg,.png"
                            className="hidden"
                        // onChange={handleFileChange}
                        // disabled={isSubmitting}
                        />
                        <label
                            htmlFor="profilePhoto"
                            className={`flex  justify-start items-center gap-5 border-2 border-dashed border-gray-300 rounded-3xl p-6 cursor-pointer group-hover:border-primary transition-colors `} // ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
                        >
                            <div className="p-2 border-dashed border-2 border-gray-300 rounded-full group-hover:border-primary transition-colors">
                                <div className="w-16 h-16 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                                    {/* {profileImageFile || formData.profile_image ? (
                                    <Image
                                        src={ProfilePicture}
                                        alt="preview"
                                        fill
                                        className="w-full h-full object-cover"
                                    />
                                ) : ( */}
                                    <Camera stroke="white" size={30} />
                                    {/* )} */}
                                </div>
                            </div>
                            <div className='text-right'>
                                <h3 className=" mb-2">عکس پروفایل</h3>
                                <p className="text-sm text-gray-500 mb-1">فرمت مورد قبول: *.jpg, *.png, *.jpeg</p>
                                <p className="text-sm">حداکثر حجم فایل: 3 مگابایت</p>
                            </div>
                        </label>
                    </div>
                    <div className='flex flex-col gap-3 col-span-2'>
                        <label htmlFor="">درباره فروشگاه</label>
                        <textarea name="" id="" cols={30} rows={5} placeholder='درباره نوع فعالیت یا فروشگاه خودتان اینجا بنویسید' className='border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 '></textarea>
                    </div>
                    <div className='flex flex-col gap-3'>
                        <label htmlFor="name"> شماره تلفن فروشگاه  </label>
                        <input type="text" name='name' className='border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                    </div>
                    <div className='flex flex-col gap-3'>
                        <label htmlFor="name"> ایمیل فروشگاه  </label>
                        <input type="text" name='name' className='border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 ' />
                    </div>
                    <div className='col-span-2'>
                        <NeshanAddressForm />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ShopInfoPage