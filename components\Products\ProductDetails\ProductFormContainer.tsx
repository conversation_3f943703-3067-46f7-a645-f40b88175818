
"use client"
import DropdownSearch, { DropdownItem, Category } from "@/components/common/DropdownSearch"
import { Camera, Clock, FilePlus, Trash2 } from "lucide-react"
import productImage from "@/public/assets/images/man-looking-car-thinking-purchase_1303-14587.png"
// import RichTextEditor from "./Tiptap"
import dynamic from "next/dynamic"
import { useState } from "react"
import Image from "next/image"
const RichTextEditor = dynamic(() => import("./Tiptap"), { ssr: false })

// Sample hierarchical categories data
const categories: Category[] = [
    {
        id: 1,
        label: "خودرو",
        children: [
            {
                id: 11,
                label: "سواری",
                children: [
                    { id: 111, label: "پژو" },
                    { id: 112, label: "سمند" },
                    { id: 113, label: "پراید" },
                    { id: 114, label: "دنا" },
                    { id: 115, label: "تیبا" },
                ]
            },
            {
                id: 12,
                label: "وانت",
                children: [
                    { id: 121, label: "پیکان وانت" },
                    { id: 122, label: "زامیاد" },
                    { id: 123, label: "آریسان" },
                ]
            },
            {
                id: 13,
                label: "موتورسیکلت",
                children: [
                    { id: 131, label: "هوندا" },
                    { id: 132, label: "یاماها" },
                    { id: 133, label: "باجاج" },
                    { id: 134, label: "کویر موتور" },
                ]
            },
            {
                id: 14,
                label: "لوازم یدکی",
                children: [
                    { id: 141, label: "روغن موتور" },
                    { id: 142, label: "فیلتر" },
                    { id: 143, label: "لاستیک" },
                    { id: 144, label: "باتری" },
                ]
            }
        ]
    },
    {
        id: 2,
        label: "املاک",
        children: [
            {
                id: 21,
                label: "فروش مسکونی",
                children: [
                    { id: 211, label: "آپارتمان" },
                    { id: 212, label: "خانه ویلایی" },
                    { id: 213, label: "زمین مسکونی" },
                ]
            },
            {
                id: 22,
                label: "اجاره مسکونی",
                children: [
                    { id: 221, label: "آپارتمان" },
                    { id: 222, label: "خانه ویلایی" },
                    { id: 223, label: "سوئیت و استودیو" },
                ]
            }
        ]
    },
    {
        id: 3,
        label: "کالای دیجیتال",
        children: [
            {
                id: 31,
                label: "موبایل",
                children: [
                    { id: 311, label: "سامسونگ" },
                    { id: 312, label: "اپل" },
                    { id: 313, label: "شیائومی" },
                    { id: 314, label: "هوآوی" },
                ]
            },
            {
                id: 32,
                label: "لپ‌تاپ",
                children: [
                    { id: 321, label: "ایسوس" },
                    { id: 322, label: "اچ پی" },
                    { id: 323, label: "لنوو" },
                    { id: 324, label: "مک‌بوک" },
                ]
            }
        ]
    }
];
const ProductFormContainer = () => {

    const [categoryItem, setCategoryItem] = useState<Category | null>(null);
    const [subCategoryItem, setSubCategoryItem] = useState<DropdownItem | null>(null);
    const [images, setImages] = useState<string[]>([])

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        debugger
        const files = e.target.files
        if (!files) return

        const newImages: string[] = []

        Array.from(files).forEach((file) => {
            const previewUrl = URL.createObjectURL(file)
            newImages.push(previewUrl)
        })

        setImages((prev) => [...prev, ...newImages])
    }

    const handleDelete = (indexToRemove: number) => {
        setImages((prev) => prev.filter((_, i) => i !== indexToRemove))
    }

    return (
        <div className="md:grid grid-cols-7 max-md:px-3 gap-8 max-md:flex max-md:flex-col">
            <div id="right_section" className="col-span-5 bg-white rounded-2xl p-5 border border-gray-200">
                <div className="add-product-header flex items-center gap-3 w-full mb-8">
                    <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
                        <FilePlus />
                    </div>

                    <div className="flex items-center gap-1">
                        <h2>افزودن محصول جدید</h2>
                    </div>

                    <div className="flex-1 h-px bg-gray-300 mx-2" />

                    <div className="flex items-center gap-1">
                        <h2>پیش نمایش</h2>
                    </div>
                </div>

                <div className="product-form grid grid-cols-1 gap-5">
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="persian_title" className="px-1"> عنوان فارسی محصول</label>
                        <input type="text" name="persian_title" className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary " />
                    </div>
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="english_title" className="px-1"> عنوان انگلیسی محصول</label>
                        <input type="text" name="english_title" className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary " />
                    </div>
                    <div className="flex items-center gap-5 ">
                        <div className="flex flex-col gap-3 w-full">
                            <label htmlFor=""> انتخاب دسته بندی </label>
                            <DropdownSearch
                                useModal={true}
                                categories={categories}
                                placeholder="دسته‌بندی محصول را انتخاب کنید"
                                onSelect={(item) => setCategoryItem(item as Category)}
                                selectedItem={categoryItem}
                            />
                        </div>
                        <div className="flex flex-col gap-3 w-full">
                            <label htmlFor=""> انتخاب زیر دسته (اختیاری) </label>
                            <DropdownSearch
                                items={[
                                    { id: 1, label: "زیردسته ۱" },
                                    { id: 2, label: "زیردسته ۲" },
                                    { id: 3, label: "زیردسته ۳" },
                                ]}
                                placeholder="زیردسته را انتخاب کنید"
                                onSelect={(item) => setSubCategoryItem(item as DropdownItem)}
                                selectedItem={subCategoryItem}
                            />
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="help" className="px-1"> توضیح کوتاه محصول </label>
                        <textarea name="help" id="help" className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary " rows={5} cols={50} placeholder="توضیح کوتاه محصول را اینجا وارد کنید"></textarea>
                    </div>
                    <RichTextEditor />
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="help" className="px-1"> راهنمای محصول </label>
                        <textarea name="help" id="help" className="border bg-gray-50 border-gray-300 rounded-xl p-3 focus:outline-none focus:ring-primary focus:border-primary " rows={5} cols={50} placeholder="توضیح کوتاه در مورد راهنمای محصول را اینجا وارد کنید"></textarea>
                    </div>

                </div>


            </div>
            <div id="left_section" className="col-span-2 flex flex-col gap-12">
                <div className="bg-white rounded-2xl p-5 flex flex-col gap-3 border border-gray-200">
                    <div className=" mt-3">
                        <h2 className="title-border-bottom pb-3 relative w-fit">
                            انتشار محصول
                        </h2>
                    </div>
                    <div className="flex justify-between items-center text-muted-foreground border border-gray-200 rounded-xl p-3 pt-2 pb-0">
                        <p>
                            فروش محصول فعال باشد
                        </p>
                        <div className="">
                            <label className="relative inline-block w-14 h-7 cursor-pointer group">
                                <input type="checkbox" className="sr-only peer" />
                                <span
                                    className="absolute top-0 left-0 w-full h-full bg-gray-300 rounded-full transition-colors duration-300 peer-checked:bg-blue-500"
                                ></span>
                                <span
                                    className="absolute top-[3px] left-[4px] w-6 h-6 bg-white rounded-full shadow-md transition-all duration-300
                                    peer-checked:translate-x-6
                                    peer-active:w-12
                                    peer-checked:peer-active:translate-x-2"
                                ></span>
                            </label>
                        </div>
                    </div>
                    <div className="flex flex-col gap-3 mt-5">
                        <button className="w-full text-gray-600 border border-gray-400 rounded-xl py-3 px-4 cursor-pointer hover:opacity-80">
                            ذخیره پیش نمایش
                        </button>
                        <button className="w-full text-white  bg-primary outline-0 border-0 rounded-xl py-3 px-4 cursor-pointer hover:opacity-80">
                            انتشار محصول
                        </button>
                    </div>
                    <div className="text-gray-400 bg-[#FAFAFA] flex justify-between items-center p-3 rounded-xl text-base mt-5">
                        <p className="flex items-center gap-2">
                            <span> <Clock /> </span>
                            <span>آخرین بروزرسانی: </span>
                        </p>
                        <p>
                            1404/11/23
                        </p>
                    </div>
                </div>
                <div className="bg-white rounded-2xl p-5 flex flex-col gap-3 border border-gray-200">
                    <div className=" mt-3">
                        <h2 className="title-border-bottom pb-3 relative w-fit">
                            عکس و گالری محصول
                        </h2>
                    </div>
                    <div className="group relative col-span-2">
                        <input
                            type="file"
                            id="profilePhoto"
                            accept=".jpg,.jpeg,.png"
                            className="hidden"
                        // onChange={handleFileChange}
                        // disabled={isSubmitting}
                        />
                        <div className="flex flex-col gap-3">
                            <p className="px-1">تصویر محصول</p>
                            <label
                                htmlFor="profilePhoto"
                                className={`flex bg-gray-50 justify-center items-center gap-5 border-2 border-dashed border-gray-300 rounded-2xl p-6 py-3 cursor-pointer hover:border-primary transition-colors `} // ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
                            >
                                <div className="p-2 border-gray-300 rounded-full hover:border-primary transition-colors">
                                    <div className="w-12 h-12 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                                        {/* {profileImageFile || formData.profile_image ? (
                                        <Image
                                            src={ProfilePicture}
                                            alt="preview"
                                            fill
                                            className="w-full h-full object-cover"
                                        />
                                    ) : ( */}
                                        <Camera stroke="white" size={30} />
                                        {/* )} */}
                                    </div>
                                    <p className="mt-3 text-xs">
                                        آپلود عکس
                                    </p>
                                </div>

                            </label>
                        </div>
                        <div className="flex flex-col gap-3 my-5">
                            <p className="px-1">گالری محصول</p>

                            {/* دکمه آپلود */}
                            <label
                                htmlFor="galleryUploader"
                                className="flex bg-gray-50 justify-center items-center gap-5 border-2 border-dashed border-gray-300 rounded-2xl p-6 py-3 cursor-pointer hover:border-primary transition-colors"
                            >
                                <div className="p-2 border-gray-300 rounded-full group-hover:border-primary transition-colors">
                                    <div className="w-12 h-12 bg-gray-400 flex items-center justify-center overflow-hidden rounded-full relative">
                                        <Camera stroke="white" size={30} />
                                    </div>
                                    <p className="mt-3 text-xs text-center">آپلود عکس</p>
                                </div>
                                <input
                                    id="galleryUploader"
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={handleFileChange}
                                    className="hidden"
                                />
                            </label>

                            
                            <div className="flex gap-2 flex-wrap gap-y-3 mt-4">
                                {images.map((imgSrc, index) => (
                                    <div
                                        key={index}
                                        className="relative group w-[30%] h-[58px] rounded-lg overflow-hidden"
                                    >
                                        <Image
                                            src={imgSrc}
                                            alt={`uploaded image ${index}`}
                                            fill
                                            className="object-cover"
                                        />

                                        
                                        <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                                            <button
                                                onClick={() => handleDelete(index)}
                                                className="text-white cursor-pointer bg-red-600 hover:bg-red-700 p-1 rounded-full"
                                            >
                                                <Trash2 size={16} />
                                            </button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ProductFormContainer