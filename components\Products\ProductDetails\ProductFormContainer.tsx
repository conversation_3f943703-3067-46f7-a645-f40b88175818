
"use client"
import { FilePlus } from "lucide-react"
// import RichTextEditor from "./Tiptap"
import dynamic from "next/dynamic"
const RichTextEditor = dynamic(() => import("./Tiptap"), { ssr: false })

const ProductFormContainer = () => {
    return (
        <div className="grid grid-cols-7 gap-8">
            <div id="right_section" className="col-span-5 bg-white rounded-2xl p-5 ">
                <div className="add-product-header flex items-center gap-3 w-full mb-8">
                    <div className="bg-gradient-to-t from-[#F5F6F8] to-transparent p-4.5 px-3 rounded-b-4xl">
                        <FilePlus />
                    </div>

                    <div className="flex items-center gap-1">
                        <h2>افزودن محصول جدید</h2>
                    </div>

                    <div className="flex-1 h-px bg-gray-300 mx-2" />

                    <div className="flex items-center gap-1">
                        <h2>پیش نمایش</h2>
                    </div>
                </div>

                <div className="product-form grid grid-cols-1 gap-5">
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="persian_title" className="px-1"> عنوان فارسی محصول</label>
                        <input type="text" name="persian_title" className="border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 " />
                    </div>
                    <div className="flex flex-col gap-3 ">
                        <label htmlFor="english_title" className="px-1"> عنوان انگلیسی محصول</label>
                        <input type="text" name="english_title" className="border border-gray-300 rounded-2xl p-3 focus:outline-none focus:ring-0 focus:ring-gray-500 focus:border-gray-500 " />
                    </div>
                    <RichTextEditor />
                </div>


            </div>
            <div id="left_section" className="col-span-2 bg-white rounded-2xl ">

            </div>
        </div>
    )
}

export default ProductFormContainer