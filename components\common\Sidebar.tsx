"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import Link from "next/link";

import { Minus, ChevronDown, ChevronUp, Store, LayoutDashboard, ShoppingBag, Tickets, SendToBack, ChartColumn } from "lucide-react";

export const getMenuItems = () => [
  {
    title: "پیشخوان",
    href: "/dashboard",
    type: "both",
    status: "ACTIVE",
    icon: <LayoutDashboard size={22} />,
  },
  {
    title: "اطلاعات فروشگاه",
    type: "both",
    status: "ACTIVE",
    icon: <Store size={22} />,
    submenu: [
      { title: "اطلاعات فروشگاه", href: "/store/info" },
      { title: "مدارک", href: "/store/docs" },
      { title: "اطلاعات مالک", href: "/store/owner" },
      { title: "قرارداد", href: "/store/contract" },
    ],
  },
  {
    title: "محصولات",
    type: "both",
    status: "ACTIVE",
    icon: <ShoppingBag size={22} />,
    submenu: [
      { title: "افزودن محصول", href: "/products/add" },
      { title: "همه محصولات", href: "/products" },
      { title: "نظرات و امتیازات", href: "/products/reviews" },
      { title: "سوالات پرسیده شده", href: "/products/questions" },
    ],
  },
  {
    title: "سفارشات",
    href: "/orders",
    type: "both",
    status: "ACTIVE",
    icon: <SendToBack size={22} />,
  },
  {
    title: "مالی",
    href: "/finance",
    type: "both",
    status: "ACTIVE",
    icon: <Tickets size={22} />,
  },
  {
    title: "بازاریابی و تبلیغات",
    href: "/marketing",
    type: "both",
    status: "ACTIVE",
    icon: <ChartColumn size={22} />,
  },
];


import Logo from "@/public/assets/images/logo.png";

const Sidebar = () => {
  const pathname = usePathname();
  const menuItems = getMenuItems();
  const [openIndexes, setOpenIndexes] = useState<number[]>([]);

  const toggleAccordion = (index: number) => {
    setOpenIndexes((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  return (
    <div className="md:w-[17rem] w-[20rem] h-auto">
      <div className="max-md:mt-12 md:bg-gray-200 md:border h-auto md:border-gray-200 rounded-xl relative">
        <div className="dashboard-sidbar md:w-[17rem] px-6 md:py-7 bg-white h-auto md:shadow-md rounded-xl md:absolute md:top-3 md:right-1.5 z-10 flex flex-col gap-9">
          <div className="w-[90%] h-fit">
            <Image src={Logo} alt="logo" width={128} height={64} priority />
          </div>

          <ul className="flex flex-col gap-3 relative">
            {menuItems.map((item, index) => {
              const isActive = pathname === item.href;
              const hasSubmenu = Array.isArray(item.submenu);
              const isOpen = openIndexes.includes(index);
            //   const submenuHeight = item.submenu?.length ? item.submenu.length * 32 : 0;

              return (
                <li key={index} className="relative flex flex-col">
                  
                  {hasSubmenu ? (
                    <>
                      <button
                        onClick={() => toggleAccordion(index)}
                        className="flex items-center cursor-pointer justify-between w-full text-[#363A3E] text-sm font-medium px-2 py-2 rounded-md hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          {item.icon}
                          <span>{item.title}</span>
                        </div>
                        {isOpen ? (
                          <ChevronUp size={16} className="text-gray-500" />
                        ) : (
                          <ChevronDown size={16} className="text-gray-500" />
                        )}
                      </button>

                     

                      {/* لیست زیرمنو با انیمیشن */}
                      <ul
                        className={`overflow-hidden transition-all duration-300 ease-in-out ml-5 relative ${
                          isOpen ? "max-h-[500px] opacity-100 mt-1" : "max-h-0 opacity-0"
                        }`}
                      >
                        {item.submenu!.map((sub, subIndex) => {
                          const subIsActive = pathname === sub.href;
                          return (
                            <li
                              key={subIndex}
                              className={`relative flex items-center gap-2 px-2 py-1.5 text-sm ${
                                subIsActive
                                  ? "text-yellow font-semibold"
                                  : "text-gray-600"
                              }`}
                            >
                              
                              
                              <Link 
                                href={sub.href}
                                className="w-full hover:bg-gray-100 rounded px-2 py-1 transition-colors"
                              >
                                {sub.title}
                              </Link>
                            </li>
                          );
                        })}
                      </ul>
                    </>
                  ) : (
                    <Link
                      href={item.href!}
                      className={`flex items-center gap-2 px-2 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors ${
                        isActive ? "text-yellow font-semibold" : "text-[#363A3E]"
                      }`}
                    >
                      {item.icon}
                      {item.title}
                    </Link>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;